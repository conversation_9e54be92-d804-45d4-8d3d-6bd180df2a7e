import { EntityComponentTypes, Entity<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GameM<PERSON>, Player } from "@minecraft/server";
import { system } from "@minecraft/server";
import { fixedPosRaycast } from "../../../utilities/raycasts";
/**
 * Attack timing constants for soul drain attack phases
 */
const DRAIN_START_TIMING = 43; // Start draining at tick 43 (2.15 seconds)
const DRAIN_END_TIMING = 90; // End draining at tick 90 (4.5 seconds)
/**
 * Total animation time in ticks
 */
const ANIMATION_TIME = 90; // Total attack duration is 90 ticks (4.5 seconds)
/**
 * Cooldown time in ticks after the attack completes
 */
const COOLDOWN_TIME = 20;
/**
 * Configuration for the soul drain attack
 */
const SOUL_DRAIN_CONFIG = {
    /** Maximum number of entities to drain from */
    MAX_TARGETS: 5,
    /** Maximum range for draining */
    MAX_RANGE: 32,
    /** Health percentage healed per tick of damage dealt */
    HEAL_PERCENTAGE: 0.002, // 0.02% per tick
    /** Damage dealt per tick */
    DAMAGE_PER_TICK: 1,
    /** Families that can be drained (besides players) */
    DRAINABLE_FAMILIES: ["piglin_champion", "grimhowl", "void_hydra", "wardzilla"]
};
/**
 * Executes the soul drain attack for the Necromancer using the new timing system
 * Uses localized runTimeout for draining, reset, and cooldown
 *
 * @param necromancer The necromancer entity
 */
export function executeSoulDrainAttack(necromancer) {
    // Start draining at tick 20
    let drainTiming = system.runTimeout(() => {
        try {
            const isDead = necromancer.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(drainTiming);
                return;
            }
            if (necromancer.getProperty("ptd_dbb:attack") === "soul_drain") {
                performSoulDrainAttack(necromancer);
            }
        }
        catch (error) {
            system.clearRun(drainTiming);
        }
    }, DRAIN_START_TIMING);
    // Reset attack after animation completes
    let resetTiming = system.runTimeout(() => {
        try {
            const isDead = necromancer.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(resetTiming);
                return;
            }
            if (necromancer.getProperty("ptd_dbb:attack") === "soul_drain") {
                necromancer.triggerEvent("ptd_dbb:reset_attack");
            }
        }
        catch (error) {
            system.clearRun(resetTiming);
        }
    }, ANIMATION_TIME);
    // Set cooldown after reset
    let cooldownTiming = system.runTimeout(() => {
        try {
            const isDead = necromancer.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(cooldownTiming);
                return;
            }
            necromancer.setProperty("ptd_dbb:cooling_down", false);
        }
        catch (error) {
            system.clearRun(cooldownTiming);
        }
    }, ANIMATION_TIME + COOLDOWN_TIME);
}
/**
 * Finds drainable targets around the necromancer
 * @param necromancer The necromancer entity
 * @returns Array of entities that can be drained
 */
function findDrainableTargets(necromancer) {
    const targets = [];
    // Get all entities within range
    const nearbyEntities = necromancer.dimension.getEntities({
        location: necromancer.location,
        maxDistance: SOUL_DRAIN_CONFIG.MAX_RANGE,
        excludeTypes: ["minecraft:xp_orb", "minecraft:item", "ptd_dbb:rock"],
        excludeFamilies: ["necromancer", "inanimate"]
    });
    // Separate players and boss entities
    const players = [];
    const bossEntities = [];
    for (const entity of nearbyEntities) {
        if (entity instanceof Player) {
            const gameMode = entity.getGameMode();
            if (gameMode === GameMode.creative || gameMode === GameMode.spectator) {
                continue;
            }
            players.push(entity);
        }
        else {
            // Check if entity belongs to drainable families
            for (const family of SOUL_DRAIN_CONFIG.DRAINABLE_FAMILIES) {
                const hasFamily = entity.getComponent(EntityComponentTypes.TypeFamily)?.hasTypeFamily(family);
                if (hasFamily) {
                    bossEntities.push(entity);
                    break;
                }
            }
        }
    }
    // Prioritize players first, then boss entities
    targets.push(...players);
    targets.push(...bossEntities);
    // Return maximum of 5 targets
    return targets.slice(0, SOUL_DRAIN_CONFIG.MAX_TARGETS);
}
/**
 * Performs the actual soul drain attack logic
 * @param necromancer The necromancer entity
 */
async function performSoulDrainAttack(necromancer) {
    try {
        // Find drainable targets
        const targets = findDrainableTargets(necromancer);
        if (targets.length === 0) {
            return;
        }
        // Start draining interval
        let drainInterval;
        let currentTick = 0;
        const drainDuration = DRAIN_END_TIMING - DRAIN_START_TIMING;
        drainInterval = system.runInterval(() => {
            try {
                // Check if necromancer is still alive and attack is still active
                const isDead = necromancer.getProperty("ptd_dbb:dead");
                const currentAttack = necromancer.getProperty("ptd_dbb:attack");
                if (isDead || currentAttack !== "soul_drain" || currentTick >= drainDuration) {
                    if (drainInterval !== undefined) {
                        system.clearRun(drainInterval);
                    }
                    return;
                }
                // Drain from each target
                for (const target of targets) {
                    if (target && target.dimension) {
                        // Apply damage
                        target.applyDamage(SOUL_DRAIN_CONFIG.DAMAGE_PER_TICK, {
                            cause: EntityDamageCause.magic
                        });
                        // Create raycast effect with redstone particles
                        createRaycastEffect(necromancer, target);
                    }
                }
                // Heal necromancer
                const necromancerHealth = necromancer.getComponent("minecraft:health");
                if (necromancerHealth) {
                    const maxHealth = necromancerHealth.effectiveMax;
                    const healAmount = maxHealth * SOUL_DRAIN_CONFIG.HEAL_PERCENTAGE;
                    necromancerHealth.setCurrentValue(Math.min(necromancerHealth.currentValue + healAmount, maxHealth));
                }
                currentTick++;
            }
            catch (error) {
                if (drainInterval !== undefined) {
                    system.clearRun(drainInterval);
                }
            }
        }, 1); // Run every tick
    }
    catch (error) {
        console.warn(`Error in soul drain attack: ${error}`);
    }
}
/**
 * Creates a raycast effect with redstone particles between necromancer and target
 * @param necromancer The necromancer entity
 * @param target The target entity
 */
function createRaycastEffect(necromancer, target) {
    try {
        const necromancerPos = {
            x: necromancer.location.x,
            y: necromancer.getHeadLocation().y - 1, // Offset Y for visibility
            z: necromancer.location.z
        };
        const targetPos = {
            x: target.location.x,
            y: target.getHeadLocation().y - 0.3, // Offset Y for visibility
            z: target.location.z
        };
        // Calculate distance
        const distance = Math.sqrt((targetPos.x - necromancerPos.x) ** 2 + (targetPos.y - necromancerPos.y) ** 2 + (targetPos.z - necromancerPos.z) ** 2);
        // Use the raycast utility to get positions along the ray
        const raycastPositions = fixedPosRaycast(necromancerPos, targetPos, distance, 0.5);
        // Spawn particles at each position
        for (const pos of raycastPositions) {
            necromancer.dimension.spawnParticle("minecraft:redstone_wire_dust_particle", pos);
        }
    }
    catch (error) {
        // Silently handle particle errors
    }
}
